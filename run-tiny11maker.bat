@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Tiny11 Builder with Custom Settings
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with administrator privileges - Good!
    echo.
) else (
    echo [WARNING] Not running as administrator!
    echo [INFO] Attempting to restart with admin privileges...
    echo.
    
    :: Create a temporary PowerShell script to run as admin
    echo $ErrorActionPreference = "Stop" > "%temp%\run_tiny11_admin.ps1"
    echo try { >> "%temp%\run_tiny11_admin.ps1"
    echo     Set-Location "%~dp0" >> "%temp%\run_tiny11_admin.ps1"
    echo     ^& ".\tiny11maker.ps1" >> "%temp%\run_tiny11_admin.ps1"
    echo } catch { >> "%temp%\run_tiny11_admin.ps1"
    echo     Write-Host "Error: $_" -ForegroundColor Red >> "%temp%\run_tiny11_admin.ps1"
    echo     Read-Host "Press Enter to exit" >> "%temp%\run_tiny11_admin.ps1"
    echo } >> "%temp%\run_tiny11_admin.ps1"
    
    :: Run PowerShell as admin
    powershell.exe -Command "Start-Process PowerShell -ArgumentList '-ExecutionPolicy Bypass -File \"%temp%\run_tiny11_admin.ps1\"' -Verb RunAs"
    
    echo [INFO] Admin PowerShell window should have opened.
    echo [INFO] If not, please run this batch file as administrator.
    pause
    goto :end
)

:: We're running as admin, proceed with the script
echo [INFO] Starting tiny11maker.ps1...
echo [INFO] This will take 30-60 minutes to complete.
echo [INFO] The script will prompt for drive letter and edition selection.
echo.

:: Change to script directory
cd /d "%~dp0"

:: Run the PowerShell script
powershell.exe -ExecutionPolicy Bypass -File ".\tiny11maker.ps1"

if %errorLevel% == 0 (
    echo.
    echo ========================================
    echo    Build completed successfully!
    echo ========================================
    echo.
    echo Your custom tiny11.iso should now be available in:
    echo %~dp0tiny11.iso
    echo.
    echo You can now use this ISO with Rufus to create a bootable USB.
    echo.
) else (
    echo.
    echo ========================================
    echo    Build failed with error code: %errorLevel%
    echo ========================================
    echo.
    echo Please check the tiny11.log file for details.
    echo.
)

:end
echo Press any key to exit...
pause >nul
