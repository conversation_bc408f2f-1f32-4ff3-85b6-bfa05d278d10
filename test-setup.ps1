# Simple test script to verify Windows ISO access
param (
    [string]$DriveLetter = "E:"
)

Write-Host "Testing Windows ISO access on $DriveLetter" -ForegroundColor Green

# Check if source files exist
if (Test-Path "$DriveLetter\sources\boot.wim") {
    Write-Host "✓ boot.wim found" -ForegroundColor Green
} else {
    Write-Host "✗ boot.wim NOT found" -ForegroundColor Red
}

if (Test-Path "$DriveLetter\sources\install.wim") {
    Write-Host "✓ install.wim found" -ForegroundColor Green
    
    # Get image information
    Write-Host "Getting Windows image information..." -ForegroundColor Cyan
    try {
        $imageInfo = Get-WindowsImage -ImagePath "$DriveLetter\sources\install.wim"
        Write-Host "Available Windows editions:" -ForegroundColor Yellow
        $imageInfo | Format-Table ImageIndex, ImageName -AutoSize
    } catch {
        Write-Host "Error reading image: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "✗ install.wim NOT found" -ForegroundColor Red
}

# Check admin privileges
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
Write-Host "Administrator privileges: $isAdmin" -ForegroundColor $(if($isAdmin){"Green"}else{"Yellow"})

Write-Host "Test complete!" -ForegroundColor Green
