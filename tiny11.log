﻿**********************
Windows PowerShell transcript start
Start time: 20250529143302
Username: DSK\DSK
RunAs User: DSK\DSK
Configuration Name: 
Machine: DSK (Microsoft Windows NT 10.0.22621.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe C:\Users\<USER>\Desktop\PRJ\NAS_CLOUD\_DEV\EXT_GIT_REPOS\tiny11builder\tiny11maker.ps1
Process ID: 12820
PSVersion: 5.1.22621.4391
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22621.4391
BuildVersion: 10.0.22621.4391
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Users\<USER>\Desktop\PRJ\NAS_CLOUD\_DEV\EXT_GIT_REPOS\tiny11builder\tiny11.log
Welcome to the tiny11 image creator! Release: 05-06-24
Invalid drive letter. Please enter a letter between C and Z.
Can't find Windows OS Installation files in the specified Drive Letter..
Please enter the correct DVD Drive Letter..
PS>$global:?
True
**********************
Windows PowerShell transcript end
End time: 20250529143318
**********************
