# Custom Settings Integration Guide for tiny11builder

This guide explains how your custom Windows settings have been integrated into the tiny11builder process.

## What Has Been Done

I've integrated your custom batch script settings into the tiny11builder in three ways:

### 1. **Modified tiny11maker.ps1** (Primary Integration)
Your settings have been added directly to the `tiny11maker.ps1` script in the registry modification section (around line 375). This means:
- ✅ Settings are applied **during the image creation process**
- ✅ They become part of the default user profile
- ✅ All new users will inherit these settings
- ✅ No post-installation steps required

### 2. **Enhanced autounattend.xml** 
Added a FirstLogonCommand that:
- ✅ Applies keyboard settings immediately after first login
- ✅ Restarts Explorer to ensure all changes take effect
- ✅ Runs automatically during OOBE (Out of Box Experience)

### 3. **Standalone Script** (`custom-user-settings.ps1`)
Created a separate PowerShell script that:
- ✅ Can be run on existing Windows installations
- ✅ Provides the same customizations as your original batch file
- ✅ Includes better error handling and progress feedback

## Settings Applied

Your customizations include:

### Explorer & File Management
- 🔧 Explorer opens to "This PC" instead of Quick Access
- 🔧 Removes "Shortcut" suffix from new shortcuts
- 🔧 Sets copy filename template to "filename 1"
- 🔧 Disables automatic folder type discovery
- 🔧 Enables legacy Windows Photo Viewer for all image formats

### Taskbar & Interface
- 🔧 Taskbar alignment set to left (Windows 11)
- 🔧 Disables Meet Now button
- 🔧 Hides People bar
- 🔧 Disables Weather/News/Interests
- 🔧 Disables Cortana completely

### System Performance
- 🔧 Optimized keyboard delay (0) and repeat rate (31)
- 🔧 Faster shutdown/restart (reduces app kill timeouts)
- 🔧 Auto-end tasks on shutdown

### Control Panel
- 🔧 Adds "All Tasks" view to Control Panel for power users

## How to Use

### Option A: Build Custom ISO (Recommended)
1. Run the modified `tiny11maker.ps1` script
2. Follow the prompts to select your Windows 11 ISO
3. The script will create `tiny11.iso` with all your settings pre-applied
4. Install Windows from this custom ISO

### Option B: Apply to Existing Installation
1. Run `custom-user-settings.ps1` as Administrator
2. Settings will be applied to the current user
3. Some changes require restart to take full effect

## Registry Keys Modified

The following registry locations are modified:

**User Profile (HKCU equivalent):**
- `Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced`
- `SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates`
- `Control Panel\Keyboard`
- `Control Panel\Desktop`
- `Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags`
- `SOFTWARE\Classes\.*` (for photo viewer associations)

**System-wide (HKLM):**
- `SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations`
- `SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}` (Control Panel All Tasks)
- `SOFTWARE\Policies\Microsoft\Windows\*` (Various policy settings)
- `System\CurrentControlSet\Control` (Shutdown timeouts)

## Important Notes

1. **Registry Hive Mapping**: In the build script, registry hives are temporarily mounted:
   - `HKLM\zNTUSER` = Default user profile (becomes HKCU for new users)
   - `HKLM\zSOFTWARE` = System software settings
   - `HKLM\zSYSTEM` = System configuration

2. **Compatibility**: All settings are compatible with Windows 11 22H2 and later

3. **Reversibility**: Most settings can be reversed by running the opposite registry commands

4. **Testing**: Test the custom ISO in a virtual machine before deploying to production systems

## Next Steps

1. **Run the build**: Execute `tiny11maker.ps1` to create your custom ISO
2. **Test thoroughly**: Install in a VM to verify all settings work as expected
3. **Deploy**: Use the custom ISO for clean installations

Your settings are now fully integrated into the tiny11builder workflow!
