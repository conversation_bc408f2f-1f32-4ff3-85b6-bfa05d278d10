# Automated tiny11 builder that handles all inputs automatically
# This script will run the tiny11maker.ps1 with predefined inputs

param(
    [string]$DriveLetter = "e",
    [int]$ImageIndex = 6  # Usually Windows 11 Pro
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "   Automated Tiny11 Builder" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check admin privileges
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "ERROR: This script requires administrator privileges!" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Running with administrator privileges" -ForegroundColor Green

# Verify the ISO is mounted
$drivePath = "${DriveLetter}:"
if (-not (Test-Path "$drivePath\sources\install.wim")) {
    Write-Host "ERROR: Cannot find Windows installation files on drive $drivePath" -ForegroundColor Red
    Write-Host "Please ensure your Windows 11 ISO is mounted on drive $drivePath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Windows ISO found on drive $drivePath" -ForegroundColor Green

# Get available images
try {
    $images = Get-WindowsImage -ImagePath "$drivePath\sources\install.wim"
    Write-Host "Available Windows editions:" -ForegroundColor Cyan
    $images | Format-Table ImageIndex, ImageName -AutoSize
    
    # Validate the selected index
    if ($ImageIndex -gt $images.Count -or $ImageIndex -lt 1) {
        Write-Host "WARNING: Invalid image index $ImageIndex. Using index 1 instead." -ForegroundColor Yellow
        $ImageIndex = 1
    }
    
    $selectedImage = $images | Where-Object { $_.ImageIndex -eq $ImageIndex }
    Write-Host "Selected: Index $ImageIndex - $($selectedImage.ImageName)" -ForegroundColor Green
    
} catch {
    Write-Host "ERROR: Failed to read Windows image information" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting automated build process..." -ForegroundColor Yellow
Write-Host "This will take 30-60 minutes to complete." -ForegroundColor Yellow
Write-Host ""

# Create input file for the script
$inputFile = "$env:TEMP\tiny11_input.txt"
@"
$DriveLetter
$ImageIndex
"@ | Out-File -FilePath $inputFile -Encoding ASCII

# Run the tiny11maker script with input redirection
try {
    $process = Start-Process -FilePath "powershell.exe" -ArgumentList @(
        "-ExecutionPolicy", "Bypass",
        "-Command", "& '.\tiny11maker.ps1' < '$inputFile'"
    ) -WorkingDirectory $PSScriptRoot -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "   Build completed successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "Your custom tiny11.iso is ready!" -ForegroundColor Green
        Write-Host "Location: $PSScriptRoot\tiny11.iso" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Features included:" -ForegroundColor Yellow
        Write-Host "  ✓ All tiny11 optimizations (debloated, no telemetry)" -ForegroundColor White
        Write-Host "  ✓ Your custom settings (taskbar left, photo viewer, etc.)" -ForegroundColor White
        Write-Host "  ✓ Unattended installation configured" -ForegroundColor White
        Write-Host "  ✓ Ready for Rufus or USB creation tools" -ForegroundColor White
        Write-Host ""
    } else {
        Write-Host "Build failed with exit code: $($process.ExitCode)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "ERROR: Failed to run tiny11maker script" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Clean up input file
    if (Test-Path $inputFile) {
        Remove-Item $inputFile -Force
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
