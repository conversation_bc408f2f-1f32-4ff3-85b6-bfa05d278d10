# Custom User Settings Script for tiny11builder
# This script applies your personal Windows customizations
# Can be run post-installation or integrated into the build process

Write-Host "Applying Custom User Settings..." -ForegroundColor Green

# Change Windows Explorer to automatically open 'This PC'
Write-Host "Setting Explorer to open 'This PC' by default..."
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f

# Remove the "Shortcut" suffix when creating shortcuts
Write-Host "Removing 'Shortcut' suffix from new shortcuts..."
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%s.lnk" /f

# Set suffix added to filename when copying files
Write-Host "Setting copy filename template..."
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%s 1" /f

# Set Keyboard Delay and Keyboard Repeat Rate
Write-Host "Optimizing keyboard settings..."
reg add "HKCU\Control Panel\Keyboard" /v KeyboardDelay /t REG_SZ /d 0 /f
reg add "HKCU\Control Panel\Keyboard" /v KeyboardSpeed /t REG_SZ /d 31 /f

# Disable "This App is Preventing Shutdown or Restart" Screen
Write-Host "Optimizing shutdown/restart behavior..."
REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f
REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f
REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f
REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f

# Show all tasks on control panel
Write-Host "Adding 'All Tasks' to Control Panel..."
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f

# Taskbar: Disable Meet Now
Write-Host "Disabling Meet Now..."
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

# Taskbar: Disable People
Write-Host "Disabling People bar..."
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f
REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

# Taskbar: Hide People
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f

# Taskbar: Disable Weather, News and Interests on taskbar
Write-Host "Disabling Weather and News on taskbar..."
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f

# Disable Cortana
Write-Host "Disabling Cortana..."
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f

# Disable Automatic Folder Type Discovery
Write-Host "Disabling automatic folder type discovery..."
REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f 2>nul
REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f

# Enable legacy photo viewer
Write-Host "Enabling legacy photo viewer..."
$imageExtensions = @("tif", "tiff", "bmp", "dib", "gif", "jfif", "jpe", "jpeg", "jpg", "jxr", "png")
foreach ($ext in $imageExtensions) {
    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".$ext" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f
    REG ADD "HKCU\SOFTWARE\Classes\.$ext" /ve /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f
}

# Changes the Windows 11 taskbar alignment to the left
Write-Host "Setting taskbar alignment to left..."
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v TaskbarAl /t REG_DWORD /d 0 /f

# Apply keyboard settings immediately
Write-Host "Applying keyboard settings..."
RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters

# Restart Explorer to apply changes
Write-Host "Restarting Explorer to apply changes..."
taskkill /f /im explorer.exe
start explorer.exe

Write-Host "Custom user settings applied successfully!" -ForegroundColor Green
Write-Host "Some changes may require a restart to take full effect." -ForegroundColor Yellow
