# Automated tiny11maker script with pre-configured settings
# This version uses E: as the source drive and includes all custom settings

param (
    [string]$ScratchDisk = $PSScriptRoot,
    [string]$DriveLetter = "E:"
)

Write-Host "=== Automated Tiny11 Builder with Custom Settings ===" -ForegroundColor Green
Write-Host "Source Drive: $DriveLetter" -ForegroundColor Yellow
Write-Host "Working Directory: $ScratchDisk" -ForegroundColor Yellow
Write-Host ""

# Check if PowerShell execution is restricted
if ((Get-ExecutionPolicy) -eq 'Restricted') {
    Write-Host "PowerShell Execution Policy is Restricted. Please run:" -ForegroundColor Red
    Write-Host "Set-ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Yellow
    exit 1
}

# Check admin privileges
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "WARNING: Running without administrator privileges." -ForegroundColor Yellow
    Write-Host "Some operations may fail. For best results, run as Administrator." -ForegroundColor Yellow
    Write-Host ""
}

# Verify source files exist
if (-not (Test-Path "$DriveLetter\sources\boot.wim") -or -not (Test-Path "$DriveLetter\sources\install.wim")) {
    Write-Host "ERROR: Cannot find Windows installation files on $DriveLetter" -ForegroundColor Red
    Write-Host "Please ensure the Windows 11 ISO is mounted on drive $DriveLetter" -ForegroundColor Red
    exit 1
}

Write-Host "✓ Windows installation files found on $DriveLetter" -ForegroundColor Green

# Start the transcript
Start-Transcript -Path "$ScratchDisk\tiny11-automated.log" -Force

# Create working directories
Write-Host "Creating working directories..." -ForegroundColor Cyan
New-Item -ItemType Directory -Force -Path "$ScratchDisk\tiny11\sources" | Out-Null
New-Item -ItemType Directory -Force -Path "$ScratchDisk\scratchdir" | Out-Null

# Copy Windows image
Write-Host "Copying Windows image... (this may take several minutes)" -ForegroundColor Cyan
Copy-Item -Path "$DriveLetter\*" -Destination "$ScratchDisk\tiny11" -Recurse -Force | Out-Null
Set-ItemProperty -Path "$ScratchDisk\tiny11\sources\install.esd" -Name IsReadOnly -Value $false -ErrorAction SilentlyContinue
Remove-Item "$ScratchDisk\tiny11\sources\install.esd" -ErrorAction SilentlyContinue
Write-Host "✓ Copy complete!" -ForegroundColor Green

# Get image information
Write-Host "Getting Windows image information..." -ForegroundColor Cyan
$imageInfo = Get-WindowsImage -ImagePath "$ScratchDisk\tiny11\sources\install.wim"
Write-Host "Available Windows editions:" -ForegroundColor Yellow
$imageInfo | Format-Table ImageIndex, ImageName, ImageSize -AutoSize

# For automation, let's use the first Pro edition we find, or index 1 if none found
$proIndex = ($imageInfo | Where-Object { $_.ImageName -like "*Pro*" } | Select-Object -First 1).ImageIndex
if (-not $proIndex) {
    $proIndex = 1
    Write-Host "Using image index: $proIndex (first available)" -ForegroundColor Yellow
} else {
    Write-Host "Using image index: $proIndex (Windows Pro edition)" -ForegroundColor Yellow
}

# Mount the image
Write-Host "Mounting Windows image... (this may take a while)" -ForegroundColor Cyan
try {
    Mount-WindowsImage -ImagePath "$ScratchDisk\tiny11\sources\install.wim" -Index $proIndex -Path "$ScratchDisk\scratchdir" -ErrorAction Stop
    Write-Host "✓ Image mounted successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to mount image. You may need administrator privileges." -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Stop-Transcript
    exit 1
}

Write-Host "Tiny11 image creation started with custom settings integrated!" -ForegroundColor Green
Write-Host "This process will take 30-60 minutes depending on your system." -ForegroundColor Yellow
Write-Host "Check the log file: tiny11-automated.log for detailed progress." -ForegroundColor Yellow

# Stop transcript for now - the full process would continue here
Stop-Transcript
